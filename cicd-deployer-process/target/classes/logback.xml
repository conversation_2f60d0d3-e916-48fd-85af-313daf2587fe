<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/bgdeployer.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Add a separate file for detailed Docker container operations -->
    <appender name="DOCKER_FILE" class="ch.qos.logback.core.FileAppender">
        <file>logs/docker-operations.log</file>
        <append>true</append>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} %-5level - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- Create a dedicated logger for HealthChecker to track container inspections -->
    <logger name="com.bgdeployer.service.HealthChecker" level="DEBUG">
        <appender-ref ref="DOCKER_FILE" />
    </logger>
    
    <!-- Create a dedicated logger for ComposeController to track Docker commands -->
    <logger name="com.bgdeployer.service.ComposeController" level="DEBUG">
        <appender-ref ref="DOCKER_FILE" />
    </logger>
    
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>