#!/bin/bash

# Load environment variables
source .env

# Test webhook endpoint
echo "Testing webhook endpoint..."

# GitHub webhook format
curl -X POST http://localhost:8083/webhook/github \
  -H "X-GitHub-Event: push" \
  -H "X-Hub-Signature-256: sha256=$(echo -n "{\"ref\":\"refs/heads/main\"}" | openssl dgst -sha256 -hmac "$WEBHOOK_SECRET" | cut -d' ' -f2)" \
  -H "Content-Type: application/json" \
  -d '{
    "ref": "refs/heads/main",
    "repository": {
      "full_name": "'"${GITHUB_REPO_URL##*/}"'",
      "clone_url": "'"$GITHUB_REPO_URL"'"
    },
    "commits": [{
      "id": "test-commit-id",
      "message": "Test commit from webhook",
      "author": {
        "name": "Test User",
        "email": "<EMAIL>"
      },
      "timestamp": "'"$(date -u +"%Y-%m-%dT%H:%M:%SZ")"'",
      "modified": ["README.md"]
    }],
    "pusher": {
      "name": "'"$GITHUB_USERNAME"'"
    }
  }'

echo ""
echo "Check orchestrator logs to see if webhook was processed:"
echo "docker-compose logs -f orchestrator"