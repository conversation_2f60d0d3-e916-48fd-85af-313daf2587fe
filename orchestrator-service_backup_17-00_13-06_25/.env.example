# CI/CD Orchestrator Environment Configuration Template
# Copy this file to .env and fill in your values

# Spring Profile
SPRING_PROFILES_ACTIVE=docker

# Vault Service
ORCHESTRATOR_VAULT_SERVICE_BASE_URL=http://vault-service:3000
ORCHESTRATOR_VAULT_SERVICE_API_KEY=change-me-to-secure-key

# GitHub Webhook Configuration
WEBHOOK_SECRET=change-me-to-secure-webhook-secret
WEBHOOK_LISTENER_PORT=8083

# Builder Configuration
ORCHESTRATOR_BUILDER_PYTHON_EXECUTABLE=python3
ORCHESTRATOR_BUILDER_TIMEOUT_MINUTES=30
ORCHESTRATOR_BUILDER_VAULT_URL=http://vault-service:3000/api/secrets/
ORCHESTRATOR_BUILDER_VAULT_API_KEY=change-me-to-secure-key

# Deployer Configuration
ORCHESTRATOR_DEPLOYER_JAR_PATH=/deployer.jar
ORCH<PERSON><PERSON><PERSON><PERSON>_DEPLOYER_TIMEOUT_MINUTES=20

# Repository Settings
GITHUB_REPO_URL=https://github.com/username/repo.git
GITHUB_USERNAME=your-github-username
GITHUB_TOKEN=your-github-personal-access-token

# Docker Registry Settings
REGISTRY_URL=your-registry-url
DOCKER_USERNAME=your-docker-username
DOCKER_PASSWORD=your-docker-password

# Java Options
JAVA_OPTS=-Xmx2g -Xms1g