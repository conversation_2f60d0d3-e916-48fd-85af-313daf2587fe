{"deployment_id": "015", "version": null, "color": "blue", "timestamp": "2025-05-06T13:56:44", "original_compose_file": "/home/<USER>/workspace/sample-docker-compose.yml", "services": {}, "deployment_status": "success", "health_check_results": {"web": true, "api": true}, "error": null, "deployment_type": "standard", "rolled_back_from": null, "health_checks": ["http://web-blue-app:80/health", "http://api-blue-app:8080/health"]}