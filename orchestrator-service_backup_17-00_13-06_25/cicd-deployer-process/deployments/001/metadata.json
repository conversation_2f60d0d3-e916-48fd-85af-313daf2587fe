{"deployment_id": "001", "version": null, "color": "green", "timestamp": "2025-05-06T12:55:31", "original_compose_file": "/home/<USER>/workspace/sample-docker-compose.yml", "services": {}, "deployment_status": "failed", "health_check_results": {}, "error": "Failed to execute command: docker-compose -f deployments/001/docker-compose-green.yml up -d --build --force-recreate --project-name myapp_green - Cannot run program \"docker-compose\": error=2, No such file or directory"}