version: '3'
services:
  web-green:
    image: nginx:alpine
    volumes:
    - ./web:/usr/share/nginx/html
    healthcheck:
      test:
      - CMD
      - wget
      - --quiet
      - --tries=1
      - --spider
      - http://localhost/health
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      deployment-network:
        aliases:
        - service-green-app
  api-green:
    image: node:14-alpine
    volumes:
    - ./api:/app
    working_dir: /app
    command:
    - node
    - server.js
    healthcheck:
      test:
      - CMD
      - wget
      - --quiet
      - --tries=1
      - --spider
      - http://localhost:8080/health
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      deployment-network:
        aliases:
        - service-green-app
networks:
  default:
    driver: bridge
  deployment-network:
    external: true
