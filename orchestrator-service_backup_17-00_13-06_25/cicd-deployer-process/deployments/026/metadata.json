{"deployment_id": "026", "version": null, "color": "green", "timestamp": "2025-05-06T15:51:30", "original_compose_file": "/home/<USER>/workspace/sample-docker-compose.yml", "services": {}, "deployment_status": "success", "health_check_results": {"web": true, "api": true}, "error": null, "deployment_type": "standard", "rolled_back_from": null, "health_checks": ["http://web-green-app:80/health", "http://api-green-app:8080/health"]}