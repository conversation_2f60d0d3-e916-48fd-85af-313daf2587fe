
2025-05-07 16:28:38 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/038/metadata.json
2025-05-07 16:28:38 [main] INFO  com.bgdeployer.service.HealthChecker - Starting Docker health check for container myapp_web-blue with timeout 120 seconds
2025-05-07 16:28:38 [main] INFO  com.bgdeployer.service.HealthChecker - Waiting for container myapp_web-blue to be healthy with timeout 120 seconds
2025-05-07 16:28:39 [main] WARN  com.bgdeployer.service.HealthChecker - Failed to get health status for container myapp_web-blue, exit code: 1
2025-05-07 16:28:39 [main] WARN  com.bgdeployer.service.HealthChecker - Could not determine health status for container myapp_web-blue
2025-05-07 16:28:39 [main] WARN  com.bgdeployer.service.HealthChecker - Failed to check if container myapp_web-blue is running, exit code: 1
2025-05-07 16:28:39 [main] ERROR com.bgdeployer.service.HealthChecker - Container myapp_web-blue is not running
2025-05-07 16:28:39 [main] ERROR com.bgdeployer.service.HealthChecker - Docker health check failed for service web-blue
2025-05-07 16:28:39 [main] ERROR c.bgdeployer.service.DeployerService - Health check failed, rolling back: Health check failed for service web-blue
2025-05-07 16:28:39 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/038/metadata.json
2025-05-07 16:28:39 [main] INFO  c.bgdeployer.service.DeployerService - Rolling back - tearing down idle environment: blue
2025-05-07 16:28:39 [main] INFO  c.b.service.ComposeController - Tearing down blue environment
2025-05-07 16:28:39 [main] WARN  c.b.service.ComposeController - Compose file does not exist or is null: non-existent-compose-file.yml. Trying to tear down based on project name.
2025-05-07 16:28:39 [main] DEBUG com.bgdeployer.util.DockerUtils - Docker available: Docker version 27.5.1, build 9f9e405
2025-05-07 16:28:39 [main] DEBUG com.bgdeployer.util.DockerUtils - Docker Compose available: docker-compose version 1.29.2, build 5becea4c
2025-05-07 16:28:39 [main] DEBUG c.b.service.ComposeController - Executing command: docker-compose down --project-name myapp_blue
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Stops containers and removes containers, networks, volumes, and images
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - created by `up`.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - By default, the only things removed are:
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - - Containers for services defined in the Compose file
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - - Networks defined in the `networks` section of the Compose file
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - - The default network, if one is used
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Networks and volumes defined as `external` are never removed.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Usage: down [options]
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Options:
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --rmi type              Remove images. Type must be one of:
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                               'all': Remove all images used by any service.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                               'local': Remove only images that don't have a
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                               custom tag set by the `image` field.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     -v, --volumes           Remove named volumes declared in the `volumes`
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                             section of the Compose file and anonymous volumes
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                             attached to containers.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --remove-orphans        Remove containers for services not defined in the
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                             Compose file
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     -t, --timeout TIMEOUT   Specify a shutdown timeout in seconds.
2025-05-07 16:28:40 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                             (default: 10)
2025-05-07 16:28:40 [main] ERROR com.bgdeployer.Main - Deployment error: Deployment failed during health check: Health check failed for service web-blue
com.bgdeployer.exception.DeploymentException: Deployment failed during health check: Health check failed for service web-blue
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:129)
	at com.bgdeployer.Main.handleDeployCommand(Main.java:155)
	at com.bgdeployer.Main.main(Main.java:89)
Caused by: com.bgdeployer.exception.HealthCheckException: Health check failed for service web-blue
	at com.bgdeployer.service.HealthChecker.checkHealth(HealthChecker.java:99)
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:93)
	... 2 common frames omitted

===== Deployment Summary =====
Status: FAILED
Deployment ID: 038
Color: blue
Total time: 13 sec
Error: Health check failed for service web-blue
Failed at stage: failed_health_check
=============================

