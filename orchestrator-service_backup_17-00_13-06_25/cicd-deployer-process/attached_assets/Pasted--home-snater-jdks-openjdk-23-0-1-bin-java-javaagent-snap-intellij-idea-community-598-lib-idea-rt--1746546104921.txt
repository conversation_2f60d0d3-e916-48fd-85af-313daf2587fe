/home/<USER>/.jdks/openjdk-23.0.1/bin/java -javaagent:/snap/intellij-idea-community/598/lib/idea_rt.jar=38021 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /home/<USER>/Downloads/bg_deployer/CodeCraftAI/target/classes:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.33/snakeyaml-1.33.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.2/jackson-datatype-jsr310-2.15.2.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.9/logback-classic-1.2.9.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.9/logback-core-1.2.9.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar com.bgdeployer.Main /home/<USER>/Downloads/bg_deployer/CodeCraftAI/test-compose.yml
2025-05-06 16:40:23 [main] INFO  c.b.config.ConfigurationLoader - Loading configuration from bundled resource: /config.yaml
2025-05-06 16:40:24 [main] INFO  c.b.service.NginxConfigGenerator - Loading Nginx template from resources: /nginx/nginx.conf.tpl
2025-05-06 16:40:24 [main] INFO  c.b.service.DeploymentManager - Creating deployment directory: deployments/029
2025-05-06 16:40:24 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/029/metadata.json
2025-05-06 16:40:24 [main] DEBUG com.bgdeployer.service.StateManager - Reading current live color from .env
2025-05-06 16:40:24 [main] INFO  com.bgdeployer.service.StateManager - Current live color: green
2025-05-06 16:40:24 [main] INFO  c.bgdeployer.service.DeployerService - Current live color: green
2025-05-06 16:40:24 [main] INFO  c.bgdeployer.service.DeployerService - Idle color: blue
2025-05-06 16:40:24 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/029/metadata.json
2025-05-06 16:40:24 [main] INFO  c.b.service.ComposeFileTransformer - Transforming Docker Compose file for blue environment: /home/<USER>/Downloads/bg_deployer/CodeCraftAI/test-compose.yml
2025-05-06 16:40:24 [main] INFO  c.b.service.ComposeFileTransformer - Transformed Docker Compose file written to: deployments/029/docker-compose-blue.yml
2025-05-06 16:40:24 [main] INFO  c.bgdeployer.service.DeployerService - Transformed Docker Compose file: deployments/029/docker-compose-blue.yml
2025-05-06 16:40:24 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/029/metadata.json
2025-05-06 16:40:24 [main] INFO  c.b.service.ComposeController - Bringing up blue environment with file: deployments/029/docker-compose-blue.yml
2025-05-06 16:40:25 [main] INFO  com.bgdeployer.util.DockerUtils - Docker available: Docker version 27.5.1, build 9f9e405
2025-05-06 16:40:25 [main] INFO  com.bgdeployer.util.DockerUtils - Docker Compose available: docker-compose version 1.29.2, build 5becea4c
2025-05-06 16:40:25 [main] DEBUG c.b.service.ComposeController - Executing command: docker-compose -f deployments/029/docker-compose-blue.yml up -d --build --force-recreate --project-name myapp_blue
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Builds, (re)creates, starts, and attaches to containers for a service.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Unless they are already running, this command also starts any linked services.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - The `docker-compose up` command aggregates the output of each container. When
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - the command exits, all containers are stopped. Running `docker-compose up -d`
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - starts the containers in the background and leaves them running.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - If there are existing containers for a service, and the service's configuration
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - or image was changed after the container's creation, `docker-compose up` picks
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - up the changes by stopping and recreating the containers (preserving mounted
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - volumes). To prevent Compose from picking up changes, use the `--no-recreate`
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - flag.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - If you want to force Compose to stop and recreate all containers, use the
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - `--force-recreate` flag.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Usage: up [options] [--scale SERVICE=NUM...] [--] [SERVICE...]
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - 
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController - Options:
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     -d, --detach               Detached mode: Run containers in the background,
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                print new container names. Incompatible with
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                --abort-on-container-exit.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-color                 Produce monochrome output.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --quiet-pull               Pull without printing progress information
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-deps                  Don't start linked services.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --force-recreate           Recreate containers even if their configuration
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                and image haven't changed.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --always-recreate-deps     Recreate dependent containers.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                Incompatible with --no-recreate.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-recreate              If containers already exist, don't recreate
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                them. Incompatible with --force-recreate and -V.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-build                 Don't build an image, even if it's missing.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-start                 Don't start the services after creating them.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --build                    Build images before starting containers.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --abort-on-container-exit  Stops all containers if any container was
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                stopped. Incompatible with -d.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --attach-dependencies      Attach to dependent containers.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     -t, --timeout TIMEOUT      Use this timeout in seconds for container
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                shutdown when attached or when containers are
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                already running. (default: 10)
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     -V, --renew-anon-volumes   Recreate anonymous volumes instead of retrieving
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                data from the previous containers.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --remove-orphans           Remove containers for services not defined
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                in the Compose file.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --exit-code-from SERVICE   Return the exit code of the selected service
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                container. Implies --abort-on-container-exit.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --scale SERVICE=NUM        Scale SERVICE to NUM instances. Overrides the
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -                                `scale` setting in the Compose file if present.
2025-05-06 16:40:26 [ForkJoinPool.commonPool-worker-1] INFO  c.b.service.ComposeController -     --no-log-prefix            Don't print prefix in logs.
2025-05-06 16:40:26 [main] INFO  c.b.service.ComposeController - Successfully started blue environment
2025-05-06 16:40:26 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/029/metadata.json
2025-05-06 16:40:26 [main] INFO  com.bgdeployer.util.DockerUtils - Docker available: Docker version 27.5.1, build 9f9e405
2025-05-06 16:40:27 [main] INFO  com.bgdeployer.util.DockerUtils - Docker Compose available: docker-compose version 1.29.2, build 5becea4c
2025-05-06 16:40:27 [main] INFO  com.bgdeployer.service.HealthChecker - Starting health check for http://web-blue-app:80/status/200 with timeout 60 seconds
2025-05-06 16:40:27 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:40:27 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:29 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:29 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:31 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:31 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:33 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:33 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:35 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:35 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:37 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:40:37 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:39 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:39 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:41 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:41 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:43 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:43 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:45 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:45 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:47 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:40:47 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:49 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:49 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:51 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:51 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:53 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:53 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:55 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:55 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:57 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:40:57 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:40:59 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:40:59 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:01 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:01 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:03 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:03 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:05 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:05 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:07 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:41:07 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:09 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:09 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:11 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:11 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:13 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:13 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:15 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:15 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:17 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app: Temporary failure in name resolution
2025-05-06 16:41:17 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:19 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:19 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:21 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:21 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:23 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:23 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:25 [main] DEBUG com.bgdeployer.service.HealthChecker - Failed to connect to health check endpoint http://web-blue-app:80/status/200: web-blue-app
2025-05-06 16:41:25 [main] DEBUG com.bgdeployer.service.HealthChecker - Health check failed for http://web-blue-app:80/status/200, retrying in 2 seconds
2025-05-06 16:41:27 [main] ERROR com.bgdeployer.service.HealthChecker - Health check timed out for http://web-blue-app:80/status/200 after 60 seconds
2025-05-06 16:41:27 [main] ERROR com.bgdeployer.service.HealthChecker - Health check failed for service web-blue
2025-05-06 16:41:27 [main] ERROR c.bgdeployer.service.DeployerService - Health check failed, rolling back: Health check failed for service web-blue
2025-05-06 16:41:27 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/029/metadata.json
2025-05-06 16:41:27 [main] INFO  c.bgdeployer.service.DeployerService - Rolling back - tearing down idle environment: blue
2025-05-06 16:41:27 [main] INFO  c.b.service.ComposeController - Tearing down blue environment
2025-05-06 16:41:27 [main] WARN  c.b.service.ComposeController - Compose file does not exist or is null: non-existent-compose-file.yml. Trying to tear down based on project name.
2025-05-06 16:41:27 [main] INFO  com.bgdeployer.util.DockerUtils - Docker available: Docker version 27.5.1, build 9f9e405
2025-05-06 16:41:28 [main] INFO  com.bgdeployer.util.DockerUtils - Docker Compose available: docker-compose version 1.29.2, build 5becea4c
2025-05-06 16:41:28 [main] DEBUG c.b.service.ComposeController - Executing command: docker-compose down --project-name myapp_blue
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - Stops containers and removes containers, networks, volumes, and images
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - created by `up`.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - 
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - By default, the only things removed are:
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - 
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - - Containers for services defined in the Compose file
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - - Networks defined in the `networks` section of the Compose file
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - - The default network, if one is used
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - 
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - Networks and volumes defined as `external` are never removed.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - 
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - Usage: down [options]
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - 
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController - Options:
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -     --rmi type              Remove images. Type must be one of:
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                               'all': Remove all images used by any service.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                               'local': Remove only images that don't have a
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                               custom tag set by the `image` field.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -     -v, --volumes           Remove named volumes declared in the `volumes`
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                             section of the Compose file and anonymous volumes
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                             attached to containers.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -     --remove-orphans        Remove containers for services not defined in the
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                             Compose file
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -     -t, --timeout TIMEOUT   Specify a shutdown timeout in seconds.
2025-05-06 16:41:29 [ForkJoinPool.commonPool-worker-2] INFO  c.b.service.ComposeController -                             (default: 10)
2025-05-06 16:41:29 [main] ERROR com.bgdeployer.Main - Deployment error: Deployment failed during health check: Health check failed for service web-blue
com.bgdeployer.exception.DeploymentException: Deployment failed during health check: Health check failed for service web-blue
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:126)
	at com.bgdeployer.Main.handleDeployCommand(Main.java:138)
	at com.bgdeployer.Main.main(Main.java:72)
Caused by: com.bgdeployer.exception.HealthCheckException: Health check failed for service web-blue
	at com.bgdeployer.service.HealthChecker.checkHealth(HealthChecker.java:88)
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:90)
	... 2 common frames omitted

===== Deployment Summary =====
Status: FAILED
Deployment ID: 029
Color: blue
Total time: 1 min 5 sec
Error: Health check failed for service web-blue
Failed at stage: failed_health_check
=============================


Process finished with exit code 1
