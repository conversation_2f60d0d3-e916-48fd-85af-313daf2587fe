Thought for a couple of seconds


Below is a self‑contained, prose “module specification report” for your Blue‑Green Deployer component. You can hand this document straight to an AI agent (or an engineer) and it will know exactly what to build, how it fits into your system, what inputs/outputs it has, and what behaviors it must exhibit.

---

# Blue‑Green Deployer Module — Specification Report

## 1. Module Purpose and Context

Your existing CI/CD pipeline builds a Docker‑Compose application and simply tears down the old version and brings up the new one. This causes a brief downtime and leaves no easy rollback path. The **Blue‑Green Deployer Module** introduces zero‑downtime switching by:

1. Deploying a new version into an **idle** (“green”) environment while the current (“blue”) remains live.
2. Verifying health of the idle environment.
3. Switching traffic atomically to the new environment.
4. Shutting down the formerly live environment only once the new one is confirmed healthy.
5. Persisting state so subsequent deploys alternate between blue/green.

This module is written in Java, integrates with Docker/Docker‑Compose, and drives an Nginx proxy (or Docker network alias) to flip traffic.

---

## 2. High‑Level Workflow

1. **Read Current Live Color**

   * Load persistent state (e.g. from a `.env` file or lightweight database).
2. **Compute Idle Color**

   * If live = blue → idle = green; else idle = blue.
3. **Deploy Idle Stack**

   * Invoke Docker‑Compose to bring up `myapp_<idle>` with all services attached to a shared network.
4. **Health‑Check Idle**

   * Poll each service’s `/health` endpoint until it returns HTTP 200 or a timeout occurs.
5. **Switch Traffic**

   * Option A: Update Nginx config to point to `<service>-<idle>-app` and reload Nginx.
   * Option B: Swap a Docker network alias (`app-live`) from blue containers to green.
6. **Tear Down Old Stack**

   * Invoke Docker‑Compose down for `myapp_<old>`.
7. **Persist New Live Color**

   * Write `COLOR=<idle>` back to the state store.

---

## 3. Functional Requirements

| ID  | Requirement                                                                                   |
| --- | --------------------------------------------------------------------------------------------- |
| FR1 | Read and write the current “live” color atomically.                                           |
| FR2 | Bring up or tear down a Compose project by color (blue/green).                                |
| FR3 | Attach all containers to a shared Docker network with appropriate service‑name aliases.       |
| FR4 | Perform HTTP health checks on each service in the idle environment, with retries and timeout. |
| FR5 | Flip live traffic in one atomic operation (via Nginx reload or alias swap).                   |
| FR6 | Roll back automatically if health checks fail (destroy idle, leave live intact).              |
| FR7 | Log every step and surface errors clearly.                                                    |

---

## 4. Non‑Functional Requirements

* **Zero‑Downtime**: No request should fail during the traffic‑switch operation.
* **Idempotence**: Re‑running a deploy in the same state must not break the system.
* **Observability**: Emit logs/events at each stage; include timestamps, color names, service names, health‑check results.
* **Configurability**: Timeouts, retry intervals, network name, Nginx config path—all driven from external configuration.
* **Security**: Avoid privileged containers; use Java Docker client rather than shell‑eval where possible.

---

## 5. Module Components

1. **StateManager**

   * API: `String readLiveColor()`, `void writeLiveColor(String color)`
   * Backing store: `.env` file or simple JSON file.

2. **ComposeController**

   * API: `void up(String color)`, `void down(String color)`
   * Internally uses Docker‑Java or ProcessBuilder to call `docker-compose -p myapp_<color> up -d`.

3. **HealthChecker**

   * API: `boolean awaitHealthy(String hostAlias, int port, Duration timeout)`
   * Polls `http://hostAlias:port/health`, returns success/failure.

4. **TrafficSwitch**

   * Two strategies (configurable):

     * **NginxStrategy**:

       * `void updateConfig(String color)`
       * `void reload()`
     * **AliasStrategy**:

       * `void swapAlias(String alias, String fromColor, String toColor)`

5. **DeployerService** (orchestrator)

   * Single entry: `void deployNewVersion(File composeFile)`
   * Coordinates StateManager, ComposeController, HealthChecker, TrafficSwitch, error handling, and teardown.

---

## 6. Sequence of Calls

1. `live = StateManager.readLiveColor()`
2. `idle = (live == "blue" ? "green" : "blue")`
3. `ComposeController.up(idle)`
4. For each service in configured list:

   * `if (!HealthChecker.awaitHealthy(service + "-" + idle + "-app", servicePort, timeout)) throw DeploymentException;`
5. `TrafficSwitch.switchTo(idle)`
6. `ComposeController.down(live)`
7. `StateManager.writeLiveColor(idle)`

On any exception, perform:

```text
ComposeController.down(idle);
LOG.error("Deployment to idle failed, rolled back to " + live);
```

---

## 7. Configuration Schema

```yaml
networkName: deployment-network
services:
  - name: web
    port: 3000
  - name: api
    port: 4000
healthCheck:
  path: /health
  intervalSec: 2
  timeoutSec: 60
trafficSwitch:
  type: nginx    # or "alias"
  nginx:
    containerName: nginx_proxy
    configPath: /etc/nginx/nginx.conf
    templateFile: ./nginx/nginx.conf.tpl
  alias:
    aliasName: app-live
stateStore:
  type: file
  path: .env
```

---

## 8. Deliverable for AI Agent

* **Classes/Interfaces** for each component above, wired via dependency injection.
* **YAML/Env parser** to load configuration.
* **Exception types**: `DeploymentException`, `HealthCheckException`, `TrafficSwitchException`.
* **Unit tests** mocking Docker client and HTTP endpoints, verifying happy‑path and failure scenarios.

This report fully describes the module’s goals, inputs, outputs, components, data flows, error handling, and configuration. An AI agent can now generate the Java code, configuration files, and tests to deliver a production‑grade Blue‑Green Deployer.
