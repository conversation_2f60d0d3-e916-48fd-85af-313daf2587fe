/home/<USER>/.jdks/openjdk-23.0.1/bin/java -javaagent:/snap/intellij-idea-community/598/lib/idea_rt.jar=45901 -Dfile.encoding=UTF-8 -Dsun.stdout.encoding=UTF-8 -Dsun.stderr.encoding=UTF-8 -classpath /home/<USER>/Downloads/bg_deployer/CodeCraftAI/target/classes:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.33/snakeyaml-1.33.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.2/jackson-databind-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.2/jackson-annotations-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.2/jackson-core-2.15.2.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.2/jackson-datatype-jsr310-2.15.2.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.32/slf4j-api-1.7.32.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.9/logback-classic-1.2.9.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.9/logback-core-1.2.9.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.13/httpclient-4.5.13.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar com.bgdeployer.Main /home/<USER>/Downloads/bg_deployer/CodeCraftAI/test-compose.yml
2025-05-06 16:27:15 [main] INFO  c.b.config.ConfigurationLoader - Loading configuration from bundled resource: /config.yaml
2025-05-06 16:27:15 [main] INFO  c.b.service.NginxConfigGenerator - Loading Nginx template from resources: /nginx/nginx.conf.tpl
2025-05-06 16:27:15 [main] INFO  c.b.service.DeploymentManager - Creating deployment directory: deployments/023
2025-05-06 16:27:16 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/023/metadata.json
2025-05-06 16:27:16 [main] DEBUG com.bgdeployer.service.StateManager - Reading current live color from .env
2025-05-06 16:27:16 [main] INFO  com.bgdeployer.service.StateManager - Current live color: blue
2025-05-06 16:27:16 [main] INFO  c.bgdeployer.service.DeployerService - Current live color: blue
2025-05-06 16:27:16 [main] INFO  c.bgdeployer.service.DeployerService - Idle color: green
2025-05-06 16:27:16 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/023/metadata.json
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeFileTransformer - Transforming Docker Compose file for green environment: /home/<USER>/Downloads/bg_deployer/CodeCraftAI/test-compose.yml
2025-05-06 16:27:16 [main] ERROR c.bgdeployer.service.DeployerService - Deployment failed, rolling back: class java.util.ArrayList cannot be cast to class java.util.Map (java.util.ArrayList and java.util.Map are in module java.base of loader 'bootstrap')
2025-05-06 16:27:16 [main] DEBUG c.b.service.DeploymentManager - Wrote metadata to: deployments/023/metadata.json
2025-05-06 16:27:16 [main] INFO  c.bgdeployer.service.DeployerService - Rolling back - tearing down idle environment: green
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - Tearing down green environment
2025-05-06 16:27:16 [main] WARN  c.b.service.ComposeController - Compose file does not exist or is null: non-existent-compose-file.yml. Trying to tear down based on project name.
2025-05-06 16:27:16 [main] INFO  com.bgdeployer.util.DockerUtils - Mock mode enabled. Simulating Docker unavailability.
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - Docker or Docker Compose not found. Running in mock mode...
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Simulating execution of: docker-compose down --project-name myapp_green
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Stopping containers
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Removing containers
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Removing network
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Containers stopped and removed successfully
2025-05-06 16:27:16 [main] INFO  c.b.service.ComposeController - [MOCK] Command completed successfully
2025-05-06 16:27:16 [main] ERROR com.bgdeployer.Main - Deployment error: Deployment failed: class java.util.ArrayList cannot be cast to class java.util.Map (java.util.ArrayList and java.util.Map are in module java.base of loader 'bootstrap')
com.bgdeployer.exception.DeploymentException: Deployment failed: class java.util.ArrayList cannot be cast to class java.util.Map (java.util.ArrayList and java.util.Map are in module java.base of loader 'bootstrap')
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:148)
	at com.bgdeployer.Main.handleDeployCommand(Main.java:138)
	at com.bgdeployer.Main.main(Main.java:72)
Caused by: java.lang.ClassCastException: class java.util.ArrayList cannot be cast to class java.util.Map (java.util.ArrayList and java.util.Map are in module java.base of loader 'bootstrap')
	at com.bgdeployer.service.ComposeFileTransformer.transformServiceConfig(ComposeFileTransformer.java:152)
	at com.bgdeployer.service.ComposeFileTransformer.transformConfig(ComposeFileTransformer.java:100)
	at com.bgdeployer.service.ComposeFileTransformer.transform(ComposeFileTransformer.java:44)
	at com.bgdeployer.service.DeployerService.deployNewVersion(DeployerService.java:76)
	... 2 common frames omitted

===== Deployment Summary =====
Status: FAILED
Deployment ID: 023
Color: green
Total time: 0 sec
Error: class java.util.ArrayList cannot be cast to class java.util.Map (java.util.ArrayList and java.util.Map are in module java.base of loader 'bootstrap')
Failed at stage: failed
=============================


Process finished with exit code 1
