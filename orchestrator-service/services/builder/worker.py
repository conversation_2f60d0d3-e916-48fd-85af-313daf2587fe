import docker
import pika
import json
import logging
from builder import Builder
from config import RABBIT_URL, QUEUE_NAME
from errorHandler import <PERSON>rror<PERSON><PERSON><PERSON>, ErrorTypes

logging.basicConfig(level=logging.INFO)
# Remove all handlers from root logger to prevent duplicate logs
for h in logging.root.handlers[:]:
    logging.root.removeHandler(h)

# Only use the builder.step logger for console output
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(message)s')
console_handler.setFormatter(console_formatter)
step_logger = logging.getLogger("builder.step")
step_logger.handlers = []
step_logger.addHandler(console_handler)
step_logger.setLevel(logging.INFO)
step_logger.propagate = False

logger = logging.getLogger(__name__)

def process_message(ch, method, properties, body):
    error_handler = ErrorHandler()
    try:
        msg = json.loads(body)
        step_logger.info(f"Received message: {msg}")

        # Validate required fields
        if 'project_path' not in msg:
            error_handler.report_config_error(
                "Missing 'project_path' field in message",
                build_id=msg.get('build_id')
            )
            ch.basic_ack(delivery_tag=method.delivery_tag)
            return

        # Process the build
        try:
            builder = Builder(
                project_path=msg['project_path'],
                build_id=msg.get('build_id'),
            )
            out_compose, manifest_path = builder.build_and_push()
            step_logger.info(f"Build completed: compose={out_compose}, manifest={manifest_path}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
        except Exception as e:
            error_context = {
                "project_path": msg.get('project_path'),
                "registry": msg.get('registry'),
                "docker_info": _get_docker_info()
            }
            error_handler.report_build_error(
                f"Build process failed",
                exception=e,
                build_id=msg.get('build_id'),
                context=error_context
            )
            step_logger.info(f"Build failed: {e}")
            ch.basic_ack(delivery_tag=method.delivery_tag)
    except json.JSONDecodeError as e:
        error_handler.report_config_error(
            "Invalid message format, not valid JSON",
            exception=e
        )
        step_logger.info("Invalid message format, not valid JSON")
        ch.basic_ack(delivery_tag=method.delivery_tag)
    except Exception as e:
        error_handler.handle_error(
            ErrorTypes.UNEXPECTED_ERROR,
            "Unexpected error processing message",
            exception=e
        )
        step_logger.info(f"Unexpected error processing message: {e}")
        ch.basic_ack(delivery_tag=method.delivery_tag)

def callback(ch, method, body):
    task = json.loads(body)
    project_path = task['project_path']
    build_id = task.get('build_id')
    try:
        builder = Builder(project_path, build_id)
        out_compose, manifest_path = builder.build_and_push()
        result = {'status': 'success', 'compose': out_compose, 'manifest': manifest_path}
    except Exception as e:
        logger.error(f"Build failed: {e}")
        result = {'status': 'failed', 'error': str(e)}

    # publish result to result queue or log
    logger.info(f"Result: {result}")
    ch.basic_ack(delivery_tag=method.delivery_tag)

def _get_docker_info():
    """Get Docker system info for error reporting"""
    try:
        docker_client = docker.from_env()
        return {
            "version": docker_client.version(),
        }
    except Exception:
        return {"error": "Could not retrieve Docker information"}

    # worker.py
def start_worker():
    if not RABBIT_URL:
        logger.error("RABBIT_URL is not set. Please provide a valid RabbitMQ URL in the configuration.")
        return
    params = pika.URLParameters(RABBIT_URL)
    connection = pika.BlockingConnection(params)
    channel = connection.channel()
    channel.queue_declare(queue=QUEUE_NAME, durable=True)
    channel.basic_qos(prefetch_count=1)
    channel.basic_consume(queue=QUEUE_NAME, on_message_callback=process_message)
    logger.info('Waiting for build tasks...')
    channel.start_consuming()

if __name__ == '__main__':
    start_worker()