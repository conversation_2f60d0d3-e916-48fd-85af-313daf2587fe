#!/usr/bin/env python3
"""
Test script to verify the command-line interface works correctly.
"""

import subprocess
import sys
import tempfile
import os
from pathlib import Path

def test_help():
    """Test that help message is displayed correctly"""
    print("Testing help message...")
    result = subprocess.run([sys.executable, "builder.py", "--help"], 
                          capture_output=True, text=True)
    assert result.returncode == 0
    assert "Build and push Docker Compose project images" in result.stdout
    assert "project_path" in result.stdout
    assert "--build_id" in result.stdout
    print("✓ Help message test passed")

def test_missing_args():
    """Test that missing arguments are handled correctly"""
    print("Testing missing arguments...")
    result = subprocess.run([sys.executable, "builder.py"], 
                          capture_output=True, text=True)
    assert result.returncode != 0
    assert "required" in result.stderr.lower() or "error" in result.stderr.lower()
    print("✓ Missing arguments test passed")

def test_invalid_project_path():
    """Test that invalid project path is handled correctly"""
    print("Testing invalid project path...")
    result = subprocess.run([sys.executable, "builder.py", "/nonexistent/path"], 
                          capture_output=True, text=True)
    assert result.returncode != 0
    print("✓ Invalid project path test passed")

def create_test_project():
    """Create a minimal test project for testing"""
    temp_dir = tempfile.mkdtemp()
    project_path = Path(temp_dir) / "test_project"
    project_path.mkdir()
    
    # Create a minimal docker-compose.yml
    compose_content = """
version: '3.8'
services:
  test-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
"""
    
    # Create a minimal Dockerfile
    dockerfile_content = """
FROM alpine:latest
RUN echo "Hello World" > /app/hello.txt
EXPOSE 8080
CMD ["echo", "Hello from test service"]
"""
    
    with open(project_path / "docker-compose.yml", "w") as f:
        f.write(compose_content)
    
    with open(project_path / "Dockerfile", "w") as f:
        f.write(dockerfile_content)
    
    return str(project_path)

def test_dry_run():
    """Test with a real project but expect it to fail due to missing registry config"""
    print("Testing with test project (expecting config-related failure)...")
    
    # Use the existing build_directory as test project
    test_project = "./build_directory"
    if not os.path.exists(test_project):
        print("⚠ Skipping dry run test - build_directory not found")
        return
    
    result = subprocess.run([
        sys.executable, "builder.py", 
        test_project, 
        "--build_id", "test-build-123"
    ], capture_output=True, text=True, timeout=30)
    
    # We expect this to fail due to missing registry configuration, but it should
    # at least parse arguments correctly and start the build process
    print(f"Return code: {result.returncode}")
    print(f"Stdout: {result.stdout}")
    print(f"Stderr: {result.stderr}")
    
    # Check that it at least tried to start the build
    output = result.stdout + result.stderr
    assert "test-build-123" in output or "Starting build" in output or "BUILD" in output
    print("✓ Dry run test passed (arguments parsed correctly)")

def main():
    """Run all tests"""
    print("Running CLI tests for builder.py...")
    print("=" * 50)
    
    try:
        test_help()
        test_missing_args()
        test_invalid_project_path()
        test_dry_run()
        
        print("=" * 50)
        print("✅ All CLI tests passed!")
        print("\nThe builder.py command-line interface is working correctly.")
        print("\nUsage:")
        print("  python3 builder.py <project_path> --build_id <build_id>")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
