#!/usr/bin/env python3
"""
Example usage of the Builder Service with command-line interface.

This script demonstrates how to use the Builder Service without ActiveMQ/RabbitMQ.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_builder(project_path, build_id=None):
    """
    Run the builder with the specified parameters.
    
    Args:
        project_path (str): Path to the project directory
        build_id (str, optional): Build ID to use
    
    Returns:
        int: Return code from the builder process
    """
    cmd = [sys.executable, "builder.py", project_path]
    
    if build_id:
        cmd.extend(["--build_id", build_id])
    
    print(f"Running command: {' '.join(cmd)}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, check=False)
        return result.returncode
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        return 1

def main():
    """
    Main function demonstrating different usage patterns.
    """
    print("Builder Service - Command Line Usage Examples")
    print("=" * 60)
    
    # Check if build_directory exists
    test_project = "./build_directory"
    if not os.path.exists(test_project):
        print(f"❌ Test project not found: {test_project}")
        print("Please ensure the build_directory exists in the current directory.")
        return 1
    
    print(f"Using test project: {test_project}")
    print()
    
    # Example 1: Basic usage with auto-generated build ID
    print("Example 1: Basic usage with auto-generated build ID")
    print("Command: python3 builder.py ./build_directory")
    choice = input("Run this example? (y/N): ").strip().lower()
    if choice == 'y':
        return_code = run_builder(test_project)
        print(f"Build finished with return code: {return_code}")
        print()
    
    # Example 2: Usage with custom build ID
    print("Example 2: Usage with custom build ID")
    print("Command: python3 builder.py ./build_directory --build_id v1.2.3")
    choice = input("Run this example? (y/N): ").strip().lower()
    if choice == 'y':
        return_code = run_builder(test_project, "v1.2.3")
        print(f"Build finished with return code: {return_code}")
        print()
    
    # Example 3: Usage with timestamp-based build ID
    from datetime import datetime
    timestamp_build_id = f"build-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
    print("Example 3: Usage with timestamp-based build ID")
    print(f"Command: python3 builder.py ./build_directory --build_id {timestamp_build_id}")
    choice = input("Run this example? (y/N): ").strip().lower()
    if choice == 'y':
        return_code = run_builder(test_project, timestamp_build_id)
        print(f"Build finished with return code: {return_code}")
        print()
    
    print("=" * 60)
    print("Examples completed!")
    print()
    print("Key points:")
    print("- No more dependency on ActiveMQ/RabbitMQ")
    print("- Direct command-line execution")
    print("- Build ID can be auto-generated or specified")
    print("- All build artifacts are saved locally")
    print()
    print("For more information, run: python3 builder.py --help")

if __name__ == "__main__":
    main()
