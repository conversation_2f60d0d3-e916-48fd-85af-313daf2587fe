#!/usr/bin/env python3
"""
Main entry point for the Builder Service.
This script provides a simple wrapper around builder.py for convenience.
"""

import sys
import subprocess

def main():
    """
    Main entry point that delegates to builder.py
    """
    if len(sys.argv) < 2:
        print("Usage: python3 main.py <project_path> [--build_id <build_id>]")
        print("   or: python3 builder.py <project_path> [--build_id <build_id>]")
        sys.exit(1)

    # Pass all arguments to builder.py
    cmd = [sys.executable, "builder.py"] + sys.argv[1:]
    try:
        result = subprocess.run(cmd, check=True)
        sys.exit(result.returncode)
    except subprocess.CalledProcessError as e:
        sys.exit(e.returncode)
    except KeyboardInterrupt:
        print("\nBuild interrupted by user")
        sys.exit(1)

if __name__ == '__main__':
    main()
