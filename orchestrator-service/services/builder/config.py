import requests
import os

# Vault API configuration
VAULT_URL = os.getenv('VAULT_URL', 'http://node-service:3000/api/secrets/')
VAULT_API_KEY = 'your-secure-api-key' #TODO: Remooooove this hardcoded key in production!

HEADERS = {
    'x-api-key': VAULT_API_KEY,
    'Content-Type': 'application/json'
}

# Mapping entre le nom du secret et le champ de valeur dans 'data'
SECRET_VALUE_FIELDS = {
    'registry-url': 'url',
    'docker-username': 'username',
    'docker-password': 'password',
    'scan-timeout': 'timeout',
    'retry-count': 'count',
    'health-check-retries': 'retries',
    'health-check-delay': 'delay',
    'versioning-strategy': 'strategy',
    'log-dir': 'dir',
    'log-file': 'file',
}

# Fetch all secrets from Vault
secrets = {}
try:
    resp = requests.get(VAULT_URL, headers=HEADERS, timeout=5)
    resp.raise_for_status()
    secrets_list = resp.json().get('secrets', []) if resp.content else []
    for secret in secrets_list:
        name = secret.get('name')
        data = secret.get('data', {})
        value_field = SECRET_VALUE_FIELDS.get(name)
        if value_field and value_field in data:
            secrets[name] = data[value_field]
except Exception as e:
    print(f"[config.py] Failed to fetch secrets from Vault: {e}")


def get_secret(key, default=None):
    # Try to get from fetched secrets, fallback to env
    return secrets.get(key) or default

# Docker registry settings
REGISTRY_URL = get_secret('registry-url')
DOCKER_USERNAME = get_secret('docker-username')
DOCKER_PASSWORD = get_secret('docker-password')

# Retry settings
RETRY_COUNT = int(get_secret('retry-count', '3'))

# Build configuration
VERSIONING_STRATEGY = get_secret('versioning-strategy', 'uuid')  # uuid|timestamp|git|sequential
SCAN_TIMEOUT = int(get_secret('scan-timeout', '300'))  # seconds

# Health check configuration
HEALTH_CHECK_RETRIES = int(get_secret('health-check-retries', '5'))
HEALTH_CHECK_DELAY = int(get_secret('health-check-delay', '5'))  # seconds

LOG_DIR = get_secret('log-dir', 'logs')
LOG_FILE = get_secret('log-file', os.path.join(LOG_DIR, 'builder.log'))