import pika
import json

from config import (
    RABBIT_URL,
    QUEUE_NAME
)

class JobSender:
    def __init__(self, amqp_url=RABBIT_URL, queue=QUEUE_NAME):
        self.amqp_url = amqp_url
        self.queue = queue

    def send_build_job(self, project_path, build_id=None):
        if not isinstance(self.amqp_url, str) or not self.amqp_url:
            raise ValueError("AMQP URL must be a non-empty string.")
        params = pika.URLParameters(self.amqp_url)
        connection = pika.BlockingConnection(params)
        channel = connection.channel()
        task = {"project_path": project_path}
        if build_id:
            task["build_id"] = build_id
        channel.basic_publish(
            exchange='',
            routing_key=self.queue,
            body=json.dumps(task),
            properties=pika.BasicProperties(delivery_mode=2)
        )
        print(f" [x] Task sent for project: {project_path}")
        connection.close()

if __name__ == "__main__":
    import sys
    import argparse
    parser = argparse.ArgumentParser(description="Send a build job to the queue.")
    parser.add_argument("project_path", nargs="?", default=None, help="Path to the project directory")
    parser.add_argument("--build-id", dest="build_id", default=None, help="Optional build ID")
    args = parser.parse_args()

    if not args.project_path:
        print("Usage: python jobSender.py <project_path> [--build-id BUILD_ID]")
        sys.exit(1)

    sender = JobSender()
    sender.send_build_job(
        project_path=args.project_path,
        build_id=args.build_id
    )
    print(f"Build job sent for project: {args.project_path}")
