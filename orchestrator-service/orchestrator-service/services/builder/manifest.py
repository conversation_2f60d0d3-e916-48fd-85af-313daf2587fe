import os
import json
from datetime import datetime

class Manifest:
    def __init__(self, build_id, project_path):
        self.status = "started"
        self.build_id = build_id
        self.project_path = project_path
        self.timestamp = datetime.now().isoformat() + 'Z'
        self.images = {}
        self.generated_compose = None
        self.dir = os.path.join(project_path, 'builds')
        os.makedirs(self.dir, exist_ok=True)
        self.path = os.path.join(self.dir, f'{build_id}.json')

    def add_image(self, service, tag):
        self.images[service] = tag
        
    def mark_failed(self, error: str):
        self.status = "failed"
        self.error = error  # Add error field

    def set_generated_compose(self, path):
        self.generated_compose = path

    def save(self):
        data = {
            'status': self.status,
            'error': getattr(self, 'error', None),
            'build_id': self.build_id,
            'timestamp': self.timestamp,
            'images': self.images,
            'generated_compose': self.generated_compose,
            'project_path': self.project_path
        }
        with open(self.path, 'w') as f:
            json.dump(data, f, indent=2)