import logging
import traceback
import sys
import json
import os
from datetime import datetime

class ErrorTypes:
    BUILD_ERROR = "BUILD_ERROR"
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR"
    DOCKER_ERROR = "DOCKER_ERROR"
    NETWORK_ERROR = "NETWORK_ERROR"
    FILESYSTEM_ERROR = "FILESYSTEM_ERROR"
    UNEXPECTED_ERROR = "UNEXPECTED_ERROR"

class ErrorHandler:
    def __init__(self, log_dir="./logs"):
        self.logger = logging.getLogger("deepbuilder.errors")
        self.log_dir = log_dir
        os.makedirs(log_dir, exist_ok=True)
        
    def handle_error(self, error_type, message, exception=None, build_id=None, context=None):
        """
        Handle and log an error with detailed information
        
        Args:
            error_type (str): Type of error from ErrorTypes
            message (str): Human-readable error message
            exception (Exception, optional): The exception that was raised
            build_id (str, optional): Related build ID
            context (dict, optional): Additional error context
            
        Returns:
            dict: Error details that were logged
        """
        error_details = {
            "error_type": error_type,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "build_id": build_id
        }
        
        if exception:
            error_details["exception"] = {
                "type": type(exception).__name__,
                "message": str(exception),
                "traceback": traceback.format_exc()
            }
        
        if context:
            error_details["context"] = context
            
        # Log to console
        self.logger.error(f"{error_type}: {message}")
        if exception:
            self.logger.error(f"Exception: {type(exception).__name__}: {str(exception)}")
        
        # Save detailed error report to file
        if build_id:
            error_file = os.path.join(self.log_dir, f"error_{build_id}_{datetime.now().strftime('%Y%m%d%H%M%S')}.json")
            try:
                with open(error_file, 'w') as f:
                    json.dump(error_details, f, indent=2)
            except Exception as e:
                self.logger.error(f"Failed to write error log: {str(e)}")
        
        return error_details
        
    def report_build_error(self, message, exception=None, build_id=None, context=None):
        return self.handle_error(ErrorTypes.BUILD_ERROR, message, exception, build_id, context)
        
    def report_config_error(self, message, exception=None, build_id=None, context=None):
        return self.handle_error(ErrorTypes.CONFIGURATION_ERROR, message, exception, build_id, context)
        
    def report_docker_error(self, message, exception=None, build_id=None, context=None):
        return self.handle_error(ErrorTypes.DOCKER_ERROR, message, exception, build_id, context)