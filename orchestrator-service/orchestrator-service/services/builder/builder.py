import json
import os
import re
import subprocess
import uuid
import yaml
import logging
import time
import requests
from pathlib import Path
from typing import Tuple, Dict, List, Optional
from datetime import datetime
import copy
import logging.handlers
from dotenv import load_dotenv
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from exceptions import <PERSON><PERSON>erException, BuilderHealthCheckException, BuilderSecurityException, DockerException
from manifest import Manifest
from config import (
    REGISTRY_URL,
    RETRY_COUNT,
    DOCKER_USERNAME,
    DOCKER_PASSWORD,
    SCAN_TIMEOUT,
    HEALTH_CHECK_RETRIES,
    HEALTH_CHECK_DELAY,
    VERSIONING_STRATEGY,
)

# Load environment variables from .env if present
load_dotenv()
# Setup logging
LOG_DIR = os.getenv('LOG_DIR', 'logs')
os.makedirs(LOG_DIR, exist_ok=True)

handler = logging.handlers.RotatingFileHandler(
    os.path.join(LOG_DIR, 'builder.log'), maxBytes=5*1024*1024, backupCount=5
)
formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s: %(message)s')
handler.setFormatter(formatter)

# Setup logger for file (detailed)
logger = logging.getLogger("builder.file")
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# Setup separate logger for console (summary only)
step_logger = logging.getLogger("builder.step")
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_formatter = logging.Formatter('%(message)s')
console_handler.setFormatter(console_formatter)
step_logger.addHandler(console_handler)
step_logger.propagate = False
step_logger.setLevel(logging.INFO)

class Builder:
    def __init__(self, project_path: str, build_id: Optional[str] = None):
        self.project_path = Path(project_path).resolve()
        self.build_id = build_id or self._generate_build_id()
        self.manifest = Manifest(self.build_id, str(self.project_path))
        self._docker_login()
        self._last_step = None
        self._last_status = None

    def _log_step(self, step: str, status: str, detail: str = ""):
        # Always log detail to file
        logger.info(f"[{step}] {status}{' - ' + detail if detail else ''}")
        # Only print summary to console via step_logger
        if status.lower() in ("success", "failed", "warning"):
            step_logger.info(f"{step}: {status}{' - ' + detail if detail else ''}")

    def _generate_build_id(self) -> str:
        """Apply versioning strategy to generate build ID"""
        if VERSIONING_STRATEGY == "timestamp":
            return datetime.now().strftime("%Y%m%d-%H%M%S")
        elif VERSIONING_STRATEGY == "git":
            return self._get_git_commit_hash()
        elif VERSIONING_STRATEGY == "sequential":
            return self._get_next_sequential_version()
        return str(uuid.uuid4())
    
    def _get_git_commit_hash(self) -> str:
        return "commit_hash_placeholder"

    def _get_next_sequential_version(self) -> str:
        """Get next sequential version number"""
        try:
            result = subprocess.run(
                ["docker", "images", "--format", "{{.Tag}}"],
                capture_output=True,
                text=True,
                check=True
            )
            versions = [int(t) for t in result.stdout.splitlines() if t.isdigit()]
            return str(max(versions) + 1) if versions else "1"
        except subprocess.CalledProcessError:
            return "1"

    def _verify_docker_environment(self):
        """Verify Docker is running and buildx is available"""
        try:
            subprocess.run(
                ["docker", "version"],
                check=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL
            )
        except subprocess.CalledProcessError:
            raise DockerException("Docker environment not properly configured")

    def _docker_login(self) -> None:
        """Secure Docker registry authentication"""
        if not self._is_registry_accessible():
            print(self._is_registry_accessible())

        if DOCKER_USERNAME and DOCKER_PASSWORD:
            try:
                login_cmd = [
                    "docker", "login", REGISTRY_URL,
                    "--username", DOCKER_USERNAME,
                    "--password-stdin"
                ]
                # Filter out None values to avoid errors
                login_cmd = [str(x) for x in login_cmd if x is not None]
                subprocess.run(
                    login_cmd,
                    input=DOCKER_PASSWORD,
                    check=True,
                    text=True,
                    capture_output=True
                )
                logger.info(f"Authenticated with registry: {REGISTRY_URL}")
            except subprocess.CalledProcessError as e:
                logger.error(f"Registry login failed: {e.stderr}")
                raise DockerException(e.stderr)
            
    def _run_docker_command(self, command: List[str], context: Optional[str] = None, step: str = "") -> None:
        """Execute Docker command, detailed log to file, summary to console"""
        try:
            allowed = {'docker', 'docker compose', 'trivy'}
            if command[0] not in allowed:
                raise DockerException(f"Command {command[0]} not allowed in production.")
            process = subprocess.Popen(
                command,
                cwd=context,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True
            )

            if process.stdout is None:
                raise DockerException("Failed to capture process stdout.")

            last_line = ""
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    logger.info(output.strip())  # File log: all details
                    last_line = output.strip()

            if process.returncode != 0:
                self._log_step(step or command[1], "FAILED", last_line)
                raise DockerException(f"Command failed: {' '.join(command)}")
            else:
                self._log_step(step or command[1], "SUCCESS", last_line)

        except Exception as e:
            self._log_step(step or (command[1] if len(command) > 1 else command[0]), "FAILED", str(e))
            logger.error(f"Error executing Docker command: {str(e)}")
            raise

    def _build_image(self, context: str, dockerfile: str, tag: str, scan_async: bool = False) -> None:
        step = f"Build {tag}"
        logger.info(f"Building image: {tag}")
        try:
            build_cmd = [
                        "docker", "buildx", "build",
                        "-f", dockerfile,
                        "-t", tag,
                        "--progress=plain",
                        context
                    ]
            self._run_docker_command(build_cmd, context=context, step=step)
            if scan_async:
                # Launch scan in background and return immediately
                self._scan_image_async(tag)
            else:
                self._scan_image(tag)
        except DockerException as e:
            self._log_step(step, "FAILED", str(e))
            logger.error(f"Build failed for {tag}: {str(e)}")
            raise

    def _scan_image(self, image_name, scan_output_dir=None):
        step = f"Scan {image_name}"
        logger.info(f"Scanning image {image_name} for vulnerabilities")
        
        if scan_output_dir is None:
            scan_output_dir = os.path.join(self.project_path, "securityScans")
        
        os.makedirs(scan_output_dir, exist_ok=True)
        
        image_safe_name = re.sub(r'[:/]', '_', image_name)
        output_file = os.path.join(scan_output_dir, f"{image_safe_name}_scan.json")
        
        try:
            # Run Trivy scanner with JSON output
            cmd = [
                "trivy", "image",
                "--format", "json",
                "--output", output_file,
                image_name
            ]
            logger.debug(f"Running scan command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            logger.info(result.stdout)  # File log: all details
            if result.returncode != 0:
                self._log_step(step, "FAILED", result.stderr)
                logger.error(f"Scan failed: {result.stderr}")
                raise BuilderSecurityException(f"Trivy scan failed for {image_name}: {result.stderr}")
            
            # Parse scan results
            with open(output_file, 'r') as f:
                scan_results = json.load(f)
            
            # Check for critical vulnerabilities
            critical_vulns = []
            if 'Results' in scan_results:
                for result in scan_results['Results']:
                    if 'Vulnerabilities' in result:
                        for vuln in result['Vulnerabilities']:
                            if vuln.get('Severity', '').lower() == 'critical':
                                critical_vulns.append(vuln)
            
            if critical_vulns:
                self._log_step(step, "WARNING", f"Found {len(critical_vulns)} critical vulnerabilities")
                logger.warning(f"Found {len(critical_vulns)} critical vulnerabilities in {image_name}")
            else:
                self._log_step(step, "SUCCESS")
            return {
                'image': image_name,
                'scan_file': output_file,
                'vulnerabilities_found': len(critical_vulns) > 0,
                'critical_count': len(critical_vulns),
                'scan_time': datetime.now().isoformat(),
                'scan_tool': 'trivy'
            }
        except Exception as e:
            self._log_step(step, "FAILED", str(e))
            logger.exception(f"Error scanning image {image_name}")
            raise BuilderSecurityException(f"Error scanning image {image_name}: {str(e)}")

    def _push_image(self, tag: str) -> None:
        step = f"Push {tag}"
        logger.info(f"Pushing image: {tag}")
        for attempt in range(1, RETRY_COUNT + 1):
            try:
                self._run_docker_command(["docker", "push", tag], step=step)
                return
            except DockerException as e:
                if attempt == RETRY_COUNT:
                    self._log_step(step, "FAILED", str(e))
                    raise
                logger.warning(f"Push failed, retrying ({attempt}/{RETRY_COUNT}): {str(e)}")
                time.sleep(2 ** attempt)


    def _check_services_health(self, compose_file: Path) -> None:
        """Check health of deployed services"""
        logger.info("Starting service health checks")
        
        services = self._get_services_list(compose_file)
        for service in services:
            for attempt in range(1, HEALTH_CHECK_RETRIES + 1):
                try:
                    self._run_docker_command([
                        "docker", "compose", "-f", str(compose_file),
                        "exec", "-T", service,
                        "sh", "-c", "exit 0"
                    ])
                    logger.info(f"Service {service} healthy")
                    break
                except DockerException:
                    if attempt == HEALTH_CHECK_RETRIES:
                        raise BuilderHealthCheckException(f"Service {service} failed health checks")
                    time.sleep(HEALTH_CHECK_DELAY)

    def _get_services_list(self, compose_file: Path) -> List[str]:
        """Get list of services from compose file"""
        try:
            result = subprocess.run(
                ["docker","compose", "-f", str(compose_file), "ps", "--services"],
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout.splitlines()
        except subprocess.CalledProcessError as e:
            raise DockerException(f"Failed to get services: {e.stderr}")

    def _is_registry_accessible(self) -> bool:
        try:
            base_registry_url = REGISTRY_URL
            if not base_registry_url:
                logger.error("REGISTRY_URL is not set.")
                return False
            if not base_registry_url.startswith(('http://', 'https://')):
                registry_url = f'http://{base_registry_url}'
            else:
                registry_url = base_registry_url
            
            logger.info(f"Checking registry accessibility at: {registry_url}")
            response = requests.get(registry_url, verify=False, timeout=5)
            
            if response.status_code in [200, 401]:
                logger.info(f"Registry {registry_url} is accessible with status code {response.status_code}")
                return True
            else:
                logger.warning(f"Registry {registry_url} returned unexpected status code {response.status_code}")
                return False
                
        except requests.ConnectionError:
            logger.error(f"Failed to connect to registry at {registry_url}. Connection error.")
            return False
        except requests.Timeout:
            logger.error(f"Connection to registry at {registry_url} timed out.")
            return False
        except requests.RequestException as e:
            logger.error(f"An error occurred while checking registry accessibility: {str(e)}")
            return False
    
    def parse_compose(self) -> Tuple[Dict, Dict]:
        compose_file = self.project_path / 'docker-compose.yml'
        if not compose_file.exists():
            raise FileNotFoundError(f"Docker compose file not found: {compose_file}")

        with open(compose_file) as f:
            data = yaml.safe_load(f)

        services = data.get('services', {})
        build_services = {}
        
        for name, cfg in services.items():
            if 'build' in cfg:
                context = (self.project_path / cfg['build'].get('context', '.')).resolve()
                dockerfile = cfg['build'].get('dockerfile', 'Dockerfile')
                
                if not (context / dockerfile).exists():
                    raise FileNotFoundError(f"Dockerfile not found: {context/dockerfile}")

                build_services[name] = {
                    'context': str(context),
                    'dockerfile': dockerfile
                }

        return data, build_services
                
    def _ensure_log_file(self):
        """Ensure the log file and directory exist. Re-create if deleted at runtime."""
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR, exist_ok=True)
        if not os.path.exists(os.path.join(LOG_DIR, 'builder.log')):
            # Touch the file to create it
            with open(os.path.join(LOG_DIR, 'builder.log'), 'a'):
                pass

    def _build_and_push_one(self, name, params, compose_data, generated, registry_url):
        try:
            full_tag = f"{registry_url}/{name}:{self.build_id}"
            logger.info(f"Processing {name}")
            self._build_image(
                params['context'],
                os.path.join(params['context'], params['dockerfile']),
                full_tag,
                scan_async=True  # <-- enable async scan
            )
            self._push_image(full_tag)
            service_config = compose_data['services'][name].copy()
            service_config['image'] = full_tag
            if 'build' in service_config:
                del service_config['build']
            generated['services'][name] = service_config
            self.manifest.add_image(name, full_tag)
            return (name, None)
        except Exception as e:
            logger.error(f"Build/Push failed for {name}: {str(e)}", exc_info=True)
            return (name, str(e))

    def build_and_push(self) -> Tuple[str, str]:
        self._ensure_log_file()
        compose_data, to_build = self.parse_compose()
        generated = copy.deepcopy(compose_data)
        registry_url = REGISTRY_URL or ''
        if registry_url.startswith('http://'):
            registry_url = registry_url[len('http://'):]
        elif registry_url.startswith('https://'):
            registry_url = registry_url[len('https://'):]
        registry_url = registry_url.rstrip('/')
        errors = []
        # Parallel build/push
        with ThreadPoolExecutor(max_workers=min(4, len(to_build))) as executor:
            futures = [executor.submit(self._build_and_push_one, name, params, compose_data, generated, registry_url)
                       for name, params in to_build.items()]
            for future in as_completed(futures):
                name, error = future.result()
                if error:
                    errors.append((name, error))
        if errors:
            for name, err in errors:
                self._log_step(f"Build/Push {name}", "FAILED", err)
            raise BuilderException(f"Build/Push failed for: {[n for n, _ in errors]}")
        # Save generated compose
        out_dir = self.project_path / "generatedComposeFiles"
        out_dir.mkdir(parents=True, exist_ok=True)
        out_file = out_dir / f'docker-compose.{self.build_id}.yml'
        with open(out_file, 'w') as f:
            yaml.safe_dump(generated, f)
        self.manifest.set_generated_compose(str(out_file))
        self.manifest.save()
        return str(out_file), self.manifest.path

    def _scan_image_async(self, image_name, scan_output_dir=None, callback=None):
        def scan():
            try:
                result = self._scan_image(image_name, scan_output_dir)
                if callback:
                    callback(image_name, result, None)
            except Exception as e:
                if callback:
                    callback(image_name, None, e)
        t = threading.Thread(target=scan)
        t.start()
        return t

if __name__ == "__main__":
    import sys
    import argparse

    parser = argparse.ArgumentParser(
        description="Build and push Docker Compose project images.",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python3 builder.py /path/to/project
  python3 builder.py /path/to/project --build_id v1.2.3
  python3 builder.py ./build_directory --build_id my-build-123
        """
    )
    parser.add_argument("project_path", help="Path to the project directory")
    parser.add_argument("--build_id", dest="build_id", default=None,
                       help="Optional build ID (if not provided, will be auto-generated)")

    args = parser.parse_args()

    try:
        step_logger.info(f"Starting build for project: {args.project_path}")
        if args.build_id:
            step_logger.info(f"Using build ID: {args.build_id}")
        else:
            step_logger.info("Build ID will be auto-generated")

        builder = Builder(args.project_path, build_id=args.build_id)
        out_compose, manifest_path = builder.build_and_push()

        step_logger.info("=" * 60)
        step_logger.info("BUILD SUCCESSFUL!")
        step_logger.info(f"Generated compose: {out_compose}")
        step_logger.info(f"Manifest: {manifest_path}")
        step_logger.info(f"Build ID: {builder.build_id}")
        step_logger.info("=" * 60)

    except BuilderException as e:
        step_logger.info("=" * 60)
        step_logger.info(f"BUILD FAILED: {str(e)}")
        step_logger.info("=" * 60)
        sys.exit(1)
    except KeyboardInterrupt:
        step_logger.info("\nBuild interrupted by user")
        sys.exit(1)
    except Exception as e:
        step_logger.info("=" * 60)
        step_logger.info(f"UNEXPECTED ERROR: {str(e)}")
        step_logger.info("=" * 60)
        sys.exit(1)

