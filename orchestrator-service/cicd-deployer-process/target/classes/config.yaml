applicationName: myapp
networkName: deployment-network
services:
  - name: hello-web
    port: 80
healthCheck:
  path: /health
  intervalSec: 5
  timeoutSec: 120
trafficSwitch:
  type: nginx
  nginx:
    containerName: nginx_proxy
    configPath: /etc/nginx/nginx.conf
    templateFile: /nginx/nginx.conf.tpl
  alias:
    aliasName: app-live
stateStore:
  type: file
  path: .env
deployment:
  baseDir: deployments
