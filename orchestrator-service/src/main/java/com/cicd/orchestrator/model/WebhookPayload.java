package com.cicd.orchestrator.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Simple WebhookPayload model for reading webhook files created by external
 * webhook service
 * This is a minimal version to support file-based webhook processing
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class WebhookPayload {

    private String pusher;
    private Commit commit;
    private String repository;
    private String branch;

    // GitHub webhook format support
    private String ref;

    // Constructors
    public WebhookPayload() {
    }

    public WebhookPayload(String pusher, Commit commit, String repository, String branch) {
        this.pusher = pusher;
        this.commit = commit;
        this.repository = repository;
        this.branch = branch;
    }

    // Getters and Setters
    public String getPusher() {
        return pusher;
    }

    public void setPusher(String pusher) {
        this.pusher = pusher;
    }

    public Commit getCommit() {
        return commit;
    }

    public void setCommit(Commit commit) {
        this.commit = commit;
    }

    public String getRepository() {
        return repository;
    }

    public void setRepository(String repository) {
        this.repository = repository;
    }

    public String getBranch() {
        return branch != null ? branch : (ref != null ? ref.replace("refs/heads/", "") : null);
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    /**
     * Simple Commit class for webhook processing
     */
    public static class Commit {
        private String author;
        private String id;
        private String message;
        private String email;
        private String timestamp;

        @JsonProperty(value = "modifiedFiles", access = JsonProperty.Access.WRITE_ONLY)
        private java.util.List<String> modifiedFiles;

        @JsonProperty("modified_files")
        public void setModifiedFilesSnakeCase(java.util.List<String> modifiedFiles) {
            this.modifiedFiles = modifiedFiles;
        }

        // Constructors
        public Commit() {
        }

        public Commit(String author, String id, String message, String email) {
            this.author = author;
            this.id = id;
            this.message = message;
            this.email = email;
        }

        // Getters and Setters
        public String getAuthor() {
            return author;
        }

        public void setAuthor(String author) {
            this.author = author;
        }

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(String timestamp) {
            this.timestamp = timestamp;
        }

        public java.util.List<String> getModifiedFiles() {
            return modifiedFiles;
        }

        public void setModifiedFiles(java.util.List<String> modifiedFiles) {
            this.modifiedFiles = modifiedFiles;
        }

        @Override
        public String toString() {
            return "Commit{" +
                    "author='" + author + '\'' +
                    ", id='" + id + '\'' +
                    ", message='" + message + '\'' +
                    ", email='" + email + '\'' +
                    '}';
        }
    }

    @Override
    public String toString() {
        return "WebhookPayload{" +
                "pusher='" + pusher + '\'' +
                ", commit=" + commit +
                ", repository='" + repository + '\'' +
                ", branch='" + getBranch() + '\'' +
                '}';
    }
}
