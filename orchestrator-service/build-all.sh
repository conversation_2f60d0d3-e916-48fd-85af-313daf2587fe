#!/bin/bash

# Complete CI/CD Pipeline Build Script
# This script builds all components and creates the orchestrator image

set -e

echo "🚀 Building Complete CI/CD Pipeline..."

# Build deployer first
echo "🔨 Building Deployer Service..."
cd ../cicd-deployer-process
mvn clean package -Dmaven.test.skip=true
echo "✅ Deployer built successfully"

# Return to orchestrator directory and copy deployer JAR
cd ../orchestrator-service
echo "📦 Copying deployer JAR..."
cp ../cicd-deployer-process/target/blue-green-deployer-1.0-SNAPSHOT-jar-with-dependencies.jar services/deployer.jar
echo "✅ Deployer JAR copied successfully"

# Build orchestrator image
echo "🔨 Building Orchestrator Image..."
docker compose build orchestrator
echo "✅ Orchestrator image built successfully"

echo ""
echo "🎉 Complete CI/CD Pipeline built successfully!"
echo ""
echo "📋 To start the pipeline:"
echo "   docker compose up -d"
echo ""
echo "📋 To test the pipeline:"
echo "   curl -X POST http://localhost:8081/ -H 'Content-Type: application/json' -d '{\"test\": \"webhook\"}'"
echo ""
echo "📋 To monitor logs:"
echo "   docker logs cicd-orchestrator -f"
echo ""
echo "🎯 Your CI/CD pipeline is ready!"
