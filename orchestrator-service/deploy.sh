#!/bin/bash

# CI/CD Orchestrator Deployment Script
# This script ensures complete persistence across machines

set -e

echo "🚀 Starting CI/CD Orchestrator Deployment..."

# Check if <PERSON>er and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories with proper permissions
echo "📁 Creating persistent directories..."
sudo mkdir -p /var/log/ci-cd/webhook-server
sudo mkdir -p /var/log/ci-cd/builder-service
sudo chown -R $USER:$USER /var/log/ci-cd
sudo chmod -R 755 /var/log/ci-cd

# Build the orchestrator image
echo "🔨 Building orchestrator image..."
docker compose build orchestrator

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker compose down || true

# Start the complete CI/CD stack
echo "🚀 Starting CI/CD stack..."
docker compose up -d

# Wait for services to be ready
echo "⏳ Waiting for services to start..."
sleep 30

# Verify webhook service is accessible
echo "🔍 Verifying webhook service..."
if curl -s -f http://localhost:8081/ > /dev/null; then
    echo "✅ Webhook service is accessible on port 8081"
else
    echo "❌ Webhook service is not accessible"
    docker logs cicd-orchestrator --tail=20
    exit 1
fi

# Verify storage service is accessible
echo "🔍 Verifying storage service..."
if curl -s -f http://localhost:8080/health > /dev/null; then
    echo "✅ Storage service is accessible on port 8080"
else
    echo "❌ Storage service is not accessible"
    docker logs cicd-orchestrator --tail=20
    exit 1
fi

# Display service status
echo "📊 Service Status:"
docker ps | grep -E "(cicd-|orchestrator)"

echo ""
echo "🎉 CI/CD Orchestrator deployed successfully!"
echo ""
echo "📋 Service Endpoints:"
echo "   • Webhook Service: http://localhost:8081"
echo "   • Storage Service: http://localhost:8080"
echo "   • Orchestrator API: http://localhost:8082"
echo "   • Vault Service: http://localhost:8200"
echo "   • Node Vault Service: http://localhost:3001"
echo ""
echo "📝 To monitor logs:"
echo "   docker logs cicd-orchestrator -f"
echo ""
echo "🔄 To restart the stack:"
echo "   docker compose down && docker compose up -d"
echo ""
echo "🎯 Your CI/CD pipeline is ready for GitHub webhooks!"
